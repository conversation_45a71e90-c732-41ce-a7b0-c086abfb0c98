import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/merchandiser.dart';
import '../services/merchandiser_service.dart';

class MerchandiserProvider extends ChangeNotifier {
  final MerchandiserService _merchandiserService = MerchandiserService();

  List<Merchandiser> _merchandisers = [];
  bool _isLoading = false;
  String? _error;

  List<Merchandiser> get merchandisers => _merchandisers;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les merchandisers
  Future<void> chargerMerchandisers() async {
    _setLoading(true);
    try {
      _merchandisers = await _merchandiserService.obtenirTousMerchandisers();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des merchandisers: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un merchandiser
  Future<bool> ajouterMerchandiser(Merchandiser merchandiser) async {
    try {
      final id = await _merchandiserService.ajouterMerchandiser(merchandiser);
      if (id > 0) {
        _merchandisers.add(merchandiser.copyWith(id: id));
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du merchandiser: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Modifier un merchandiser
  Future<bool> modifierMerchandiser(Merchandiser merchandiser) async {
    try {
      final success = await _merchandiserService.modifierMerchandiser(
        merchandiser,
      );
      if (success) {
        final index = _merchandisers.indexWhere((p) => p.id == merchandiser.id);
        if (index != -1) {
          _merchandisers[index] = merchandiser;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            notifyListeners();
          });
        }
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du merchandiser: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Supprimer un merchandiser
  Future<bool> supprimerMerchandiser(int id) async {
    try {
      final success = await _merchandiserService.supprimerMerchandiser(id);
      if (success) {
        _merchandisers.removeWhere((p) => p.id == id);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la suppression du merchandiser: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Rechercher des merchandisers
  Future<void> rechercherMerchandisers(String terme) async {
    _setLoading(true);
    try {
      _merchandisers = await _merchandiserService.rechercherMerchandisers(
        terme,
      );
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      return await _merchandiserService.obtenirStatistiques();
    } catch (e) {
      return {'nombreTotal': 0, 'nombreActifs': 0, 'nombreInactifs': 0};
    }
  }

  // Obtenir un merchandiser par ID
  Merchandiser? obtenirMerchandiserParId(int id) {
    try {
      return _merchandisers.firstWhere((m) => m.id == id);
    } catch (e) {
      return null;
    }
  }

  // Activer/Désactiver un merchandiser
  Future<bool> toggleActivation(int id) async {
    try {
      final merchandiser = obtenirMerchandiserParId(id);
      if (merchandiser != null) {
        final merchandiserModifie = merchandiser.copyWith(
          actif: !merchandiser.actif,
        );
        return await modifierMerchandiser(merchandiserModifie);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du statut: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  void effacerErreur() {
    _error = null;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
}
