import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    return await openDatabase(
      path,
      version: 4,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Table des clients
    await db.execute('''
      CREATE TABLE clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        adresse TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        codeClient TEXT,
        matriculeFiscal TEXT,
        categorie TEXT,
        modeReglement TEXT
      )
    ''');

    // Table des produits
    await db.execute('''
      CREATE TABLE produits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        description TEXT NOT NULL,
        prix REAL NOT NULL,
        stock INTEGER NOT NULL,
        imageUrl TEXT,
        categorie TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Table des commandes
    await db.execute('''
      CREATE TABLE commandes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId INTEGER NOT NULL,
        dateCommande TEXT NOT NULL,
        statut INTEGER NOT NULL,
        montantTotal REAL NOT NULL,
        notes TEXT,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )
    ''');

    // Table des items de commande
    await db.execute('''
      CREATE TABLE commande_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        commandeId INTEGER NOT NULL,
        produitId INTEGER NOT NULL,
        nomProduit TEXT NOT NULL,
        prixUnitaire REAL NOT NULL,
        quantite INTEGER NOT NULL,
        sousTotal REAL NOT NULL,
        FOREIGN KEY (commandeId) REFERENCES commandes (id),
        FOREIGN KEY (produitId) REFERENCES produits (id)
      )
    ''');

    // Table des merchandisers
    await db.execute('''
      CREATE TABLE merchandisers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        zone TEXT NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1,
        dateCreation TEXT NOT NULL
      )
    ''');

    // Table des magasins
    await db.execute('''
      CREATE TABLE magasins (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        adresse TEXT NOT NULL,
        ville TEXT NOT NULL,
        codePostal TEXT NOT NULL,
        telephone TEXT NOT NULL,
        email TEXT,
        typeCommerce TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1,
        dateCreation TEXT NOT NULL
      )
    ''');

    // Table des parcours
    await db.execute('''
      CREATE TABLE parcours (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        description TEXT NOT NULL,
        merchandiserId INTEGER NOT NULL,
        dateVisite TEXT NOT NULL,
        statut TEXT NOT NULL DEFAULT 'planifie',
        clientIds TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        dateDebut TEXT,
        dateFin TEXT,
        commentaires TEXT,
        FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
      )
    ''');

    // Table des visites
    await db.execute('''
      CREATE TABLE visites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        parcoursId INTEGER NOT NULL,
        clientId TEXT NOT NULL,
        dateVisite TEXT NOT NULL,
        heureArrivee TEXT,
        heureDepart TEXT,
        statut TEXT NOT NULL DEFAULT 'prevue',
        commentaires TEXT,
        photos TEXT,
        donneesVisite TEXT,
        dateCreation TEXT NOT NULL,
        FOREIGN KEY (parcoursId) REFERENCES parcours (id)
      )
    ''');

    // Table des tâches de merchandising
    await db.execute('''
      CREATE TABLE taches_merchandising (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        titre TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        statut TEXT NOT NULL DEFAULT 'planifiee',
        priorite TEXT NOT NULL DEFAULT 'normale',
        merchandiserId INTEGER NOT NULL,
        clientId TEXT,
        dateEcheance TEXT NOT NULL,
        heureDebut TEXT,
        heureFin TEXT,
        dureeEstimee INTEGER,
        commentaires TEXT,
        photos TEXT,
        donnees TEXT,
        dateCreation TEXT NOT NULL,
        dateRealisation TEXT,
        rappel INTEGER NOT NULL DEFAULT 0,
        dateRappel TEXT,
        FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
      )
    ''');

    // Insérer des données de test
    await _insertSampleData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Ajouter les nouvelles tables pour la version 2
      await db.execute('''
        CREATE TABLE merchandisers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          prenom TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE,
          telephone TEXT NOT NULL,
          zone TEXT NOT NULL,
          actif INTEGER NOT NULL DEFAULT 1,
          dateCreation TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE magasins (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          adresse TEXT NOT NULL,
          ville TEXT NOT NULL,
          codePostal TEXT NOT NULL,
          telephone TEXT NOT NULL,
          email TEXT,
          typeCommerce TEXT NOT NULL,
          latitude REAL NOT NULL,
          longitude REAL NOT NULL,
          actif INTEGER NOT NULL DEFAULT 1,
          dateCreation TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE parcours (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          description TEXT NOT NULL,
          merchandiserId INTEGER NOT NULL,
          dateVisite TEXT NOT NULL,
          statut TEXT NOT NULL DEFAULT 'planifie',
          clientIds TEXT NOT NULL,
          dateCreation TEXT NOT NULL,
          dateDebut TEXT,
          dateFin TEXT,
          commentaires TEXT,
          FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE visites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          parcoursId INTEGER NOT NULL,
          magasinId INTEGER NOT NULL,
          dateVisite TEXT NOT NULL,
          heureArrivee TEXT,
          heureDepart TEXT,
          statut TEXT NOT NULL DEFAULT 'prevue',
          commentaires TEXT,
          photos TEXT,
          donneesVisite TEXT,
          dateCreation TEXT NOT NULL,
          FOREIGN KEY (parcoursId) REFERENCES parcours (id),
          FOREIGN KEY (magasinId) REFERENCES magasins (id)
        )
      ''');

      // Insérer des données de test pour les nouvelles tables
      await _insertMerchandisingData(db);
    }

    if (oldVersion < 3) {
      // Ajouter les nouveaux champs pour les clients dans la version 3
      await db.execute('ALTER TABLE clients ADD COLUMN codeClient TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN matriculeFiscal TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN categorie TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN modeReglement TEXT');
    }

    if (oldVersion < 4) {
      // Ajouter la table des tâches de merchandising dans la version 4
      await db.execute('''
        CREATE TABLE taches_merchandising (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          titre TEXT NOT NULL,
          description TEXT NOT NULL,
          type TEXT NOT NULL,
          statut TEXT NOT NULL DEFAULT 'planifiee',
          priorite TEXT NOT NULL DEFAULT 'normale',
          merchandiserId INTEGER NOT NULL,
          magasinId INTEGER,
          dateEcheance TEXT NOT NULL,
          heureDebut TEXT,
          heureFin TEXT,
          dureeEstimee INTEGER,
          commentaires TEXT,
          photos TEXT,
          donnees TEXT,
          dateCreation TEXT NOT NULL,
          dateRealisation TEXT,
          rappel INTEGER NOT NULL DEFAULT 0,
          dateRappel TEXT,
          FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id),
          FOREIGN KEY (magasinId) REFERENCES magasins (id)
        )
      ''');

      // Insérer des données de test pour les tâches
      await _insertTachesData(db);
    }
  }

  Future<void> _insertSampleData(Database db) async {
    // Clients de test
    await db.insert('clients', {
      'nom': 'Dupont',
      'prenom': 'Jean',
      'email': '<EMAIL>',
      'telephone': '0123456789',
      'adresse': '123 Rue de la Paix, 75001 Paris',
      'dateCreation': DateTime.now().toIso8601String(),
      'codeClient': 'CLI001',
      'matriculeFiscal': '1234567890123',
      'categorie': 'Particulier',
      'modeReglement': 'Carte bancaire',
    });

    await db.insert('clients', {
      'nom': 'Martin',
      'prenom': 'Marie',
      'email': '<EMAIL>',
      'telephone': '0987654321',
      'adresse': '456 Avenue des Champs, 69000 Lyon',
      'dateCreation': DateTime.now().toIso8601String(),
      'codeClient': 'CLI002',
      'matriculeFiscal': '9876543210987',
      'categorie': 'Professionnel',
      'modeReglement': 'Virement',
    });

    // Produits de test
    await db.insert('produits', {
      'nom': 'Smartphone Galaxy',
      'description': 'Smartphone Android dernière génération',
      'prix': 599.99,
      'stock': 25,
      'categorie': 'Électronique',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });

    await db.insert('produits', {
      'nom': 'Ordinateur Portable',
      'description': 'PC portable 15 pouces, 8GB RAM, SSD 256GB',
      'prix': 899.99,
      'stock': 15,
      'categorie': 'Informatique',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });

    await db.insert('produits', {
      'nom': 'Casque Audio',
      'description': 'Casque sans fil avec réduction de bruit',
      'prix': 199.99,
      'stock': 50,
      'categorie': 'Audio',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });
  }

  Future<void> _insertMerchandisingData(Database db) async {
    // Merchandisers de test
    await db.insert('merchandisers', {
      'nom': 'Dubois',
      'prenom': 'Pierre',
      'email': '<EMAIL>',
      'telephone': '0612345678',
      'zone': 'Paris Nord',
      'actif': 1,
      'dateCreation': DateTime.now().toIso8601String(),
    });

    await db.insert('merchandisers', {
      'nom': 'Leroy',
      'prenom': 'Sophie',
      'email': '<EMAIL>',
      'telephone': '0698765432',
      'zone': 'Lyon Centre',
      'actif': 1,
      'dateCreation': DateTime.now().toIso8601String(),
    });

    // Magasins de test
    await db.insert('magasins', {
      'nom': 'SuperMarché Express',
      'adresse': '15 Rue du Commerce',
      'ville': 'Paris',
      'codePostal': '75001',
      'telephone': '0142123456',
      'email': '<EMAIL>',
      'typeCommerce': 'Grande Surface',
      'latitude': 48.8566,
      'longitude': 2.3522,
      'actif': 1,
      'dateCreation': DateTime.now().toIso8601String(),
    });

    await db.insert('magasins', {
      'nom': 'Boutique Mode',
      'adresse': '28 Avenue de la Mode',
      'ville': 'Lyon',
      'codePostal': '69000',
      'telephone': '0472345678',
      'email': '<EMAIL>',
      'typeCommerce': 'Boutique',
      'latitude': 45.7640,
      'longitude': 4.8357,
      'actif': 1,
      'dateCreation': DateTime.now().toIso8601String(),
    });

    await db.insert('magasins', {
      'nom': 'TechStore',
      'adresse': '42 Boulevard Tech',
      'ville': 'Paris',
      'codePostal': '75012',
      'telephone': '0143567890',
      'email': '<EMAIL>',
      'typeCommerce': 'Électronique',
      'latitude': 48.8448,
      'longitude': 2.3727,
      'actif': 1,
      'dateCreation': DateTime.now().toIso8601String(),
    });
  }

  Future<void> _insertTachesData(Database db) async {
    final now = DateTime.now();
    final demain = now.add(Duration(days: 1));
    final apreDemain = now.add(Duration(days: 2));

    // Tâches de test
    await db.insert('taches_merchandising', {
      'titre': 'Visite SuperMarché Express',
      'description':
          'Contrôle de l\'implantation des produits et mise à jour des PLV',
      'type': 'visite',
      'statut': 'planifiee',
      'priorite': 'normale',
      'merchandiserId': 1,
      'magasinId': 1,
      'dateEcheance': demain.toIso8601String(),
      'heureDebut': demain.copyWith(hour: 9, minute: 0).toIso8601String(),
      'dureeEstimee': 120, // 2 heures
      'commentaires': 'Vérifier la mise en place de la nouvelle campagne',
      'dateCreation': now.toIso8601String(),
      'rappel': 1,
      'dateRappel': demain.copyWith(hour: 8, minute: 0).toIso8601String(),
    });

    await db.insert('taches_merchandising', {
      'titre': 'Formation équipe Boutique Mode',
      'description':
          'Formation sur les nouvelles techniques de vente et présentation produits',
      'type': 'formation',
      'statut': 'planifiee',
      'priorite': 'haute',
      'merchandiserId': 2,
      'magasinId': 2,
      'dateEcheance': apreDemain.toIso8601String(),
      'heureDebut': apreDemain.copyWith(hour: 14, minute: 0).toIso8601String(),
      'dureeEstimee': 180, // 3 heures
      'commentaires': 'Prévoir support de formation et échantillons',
      'dateCreation': now.toIso8601String(),
      'rappel': 1,
      'dateRappel': apreDemain.copyWith(hour: 13, minute: 0).toIso8601String(),
    });

    await db.insert('taches_merchandising', {
      'titre': 'Inventaire TechStore',
      'description':
          'Contrôle des stocks et identification des produits en rupture',
      'type': 'inventaire',
      'statut': 'planifiee',
      'priorite': 'normale',
      'merchandiserId': 1,
      'magasinId': 3,
      'dateEcheance': now.add(Duration(days: 3)).toIso8601String(),
      'heureDebut':
          now
              .add(Duration(days: 3))
              .copyWith(hour: 10, minute: 0)
              .toIso8601String(),
      'dureeEstimee': 240, // 4 heures
      'commentaires': 'Utiliser le scanner pour l\'inventaire',
      'dateCreation': now.toIso8601String(),
      'rappel': 0,
    });

    await db.insert('taches_merchandising', {
      'titre': 'Lancement promotion été',
      'description':
          'Mise en place des éléments promotionnels pour la campagne été',
      'type': 'promotion',
      'statut': 'planifiee',
      'priorite': 'urgente',
      'merchandiserId': 2,
      'magasinId': 1,
      'dateEcheance': now.add(Duration(days: 1)).toIso8601String(),
      'heureDebut':
          now
              .add(Duration(days: 1))
              .copyWith(hour: 8, minute: 0)
              .toIso8601String(),
      'dureeEstimee': 90, // 1h30
      'commentaires': 'Coordonner avec l\'équipe magasin pour l\'installation',
      'dateCreation': now.toIso8601String(),
      'rappel': 1,
      'dateRappel':
          now
              .add(Duration(days: 1))
              .copyWith(hour: 7, minute: 30)
              .toIso8601String(),
    });

    await db.insert('taches_merchandising', {
      'titre': 'Maintenance présentoir',
      'description': 'Réparation et entretien du présentoir principal',
      'type': 'maintenance',
      'statut': 'en_cours',
      'priorite': 'haute',
      'merchandiserId': 1,
      'magasinId': 2,
      'dateEcheance': now.toIso8601String(),
      'heureDebut': now.copyWith(hour: 11, minute: 0).toIso8601String(),
      'dureeEstimee': 60, // 1 heure
      'commentaires': 'Problème avec le système d\'éclairage',
      'dateCreation': now.subtract(Duration(hours: 2)).toIso8601String(),
      'rappel': 0,
    });
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
