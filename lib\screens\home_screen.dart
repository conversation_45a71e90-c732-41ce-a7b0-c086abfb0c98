import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/firebase_client_provider.dart';
import '../providers/produit_provider.dart';
import '../providers/commande_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/vitabrosse_logo.dart';
import '../widgets/professional_ui_components.dart';
import '../widgets/revenue_chart.dart';
import 'auth/login_screen.dart';
import 'clients/clients_screen.dart';
import 'produits/produits_screen.dart';
import 'commandes/commandes_screen.dart';
import 'devis/devis_screen.dart';
import 'catalogue/catalogue_screen.dart';
import 'merchandising/merchandising_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const ClientsScreen(),
    const ProduitsScreen(),
    const CommandesScreen(),
    const DevisScreen(),
    const CatalogueScreen(),
    const MerchandisingScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Utiliser addPostFrameCallback pour éviter setState pendant build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
      _preloadStatistics();
    });
  }

  Future<void> _preloadStatistics() async {
    try {
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );
      final commandeProvider = Provider.of<CommandeProvider>(
        context,
        listen: false,
      );

      // Preload statistics to avoid UI loading issues
      await Future.wait([
        clientProvider.obtenirStatistiques(),
        commandeProvider.obtenirStatistiques(),
      ]);

      print('Statistics preloaded successfully');
    } catch (e) {
      print('Error preloading statistics: $e');
    }
  }

  Future<void> _chargerDonnees() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final produitProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final commandeProvider = Provider.of<CommandeProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      clientProvider.loadClients(),
      produitProvider.chargerProduits(),
      produitProvider.chargerCategories(),
      commandeProvider.chargerCommandes(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) {
            final previousIndex = _selectedIndex;
            setState(() {
              _selectedIndex = index;
            });

            // Only refresh clients if we're switching TO the clients tab from another tab
            if (index == 1 && previousIndex != 1) {
              // Clients tab
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  final provider = context.read<FirebaseClientProvider>();
                  // Only refresh if the list is empty or hasn't been loaded
                  if (provider.clients.isEmpty) {
                    provider.refreshClients();
                  }
                }
              });
            }
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard_outlined),
              activeIcon: Icon(Icons.dashboard),
              label: 'Tableau de bord',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'Clients',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'Produits',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.shopping_cart_outlined),
              activeIcon: Icon(Icons.shopping_cart),
              label: 'Commandes',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.description_outlined),
              activeIcon: Icon(Icons.description),
              label: 'Devis',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.library_books_outlined),
              activeIcon: Icon(Icons.library_books),
              label: 'Catalogues',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.store_outlined),
              activeIcon: Icon(Icons.store),
              label: 'Merchandising',
            ),
          ],
        ),
      ),
    );
  }
}

class DashboardTab extends StatefulWidget {
  const DashboardTab({super.key});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab> {
  // Cache futures to prevent infinite rebuilds
  Future<Map<String, dynamic>>? _clientStatsFuture;
  Future<Map<String, dynamic>>? _productStatsFuture;
  Future<Map<String, dynamic>>? _orderStatsFuture;
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    // Defer initialization until after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeStatisticsWithDataLoading();
    });
  }

  Future<void> _initializeStatisticsWithDataLoading() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final productProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final orderProvider = Provider.of<CommandeProvider>(context, listen: false);

    try {
      // First ensure base data is loaded (only if not already loaded)
      List<Future> loadingTasks = [];

      if (clientProvider.clients.isEmpty && !clientProvider.isLoading) {
        loadingTasks.add(clientProvider.loadClients());
      }
      if (productProvider.produits.isEmpty && !productProvider.isLoading) {
        loadingTasks.add(productProvider.chargerProduits());
      }
      if (orderProvider.commandes.isEmpty && !orderProvider.isLoading) {
        loadingTasks.add(orderProvider.chargerCommandes());
      }

      if (loadingTasks.isNotEmpty) {
        await Future.wait(loadingTasks);
      }

      // Then initialize statistics futures
      _clientStatsFuture = clientProvider.obtenirStatistiques();
      _productStatsFuture = productProvider.obtenirStatistiques();
      _orderStatsFuture = orderProvider.obtenirStatistiques();

      // Trigger rebuild to show loaded data
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    } catch (e) {
      print('Error initializing dashboard data: $e');
      // Initialize with empty futures to prevent null errors
      _initializeEmptyStatistics();
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  void _initializeEmptyStatistics() {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final productProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final orderProvider = Provider.of<CommandeProvider>(context, listen: false);

    _clientStatsFuture = clientProvider.obtenirStatistiques();
    _productStatsFuture = productProvider.obtenirStatistiques();
    _orderStatsFuture = orderProvider.obtenirStatistiques();
  }

  void _refreshStatistics() {
    setState(() {
      _isInitializing = true;
    });
    _initializeStatisticsWithDataLoading();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while initializing
    if (_isInitializing) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ModernLoadingIndicator(size: 32),
              SizedBox(height: 16),
              Text(
                'Chargement du tableau de bord...',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _refreshStatistics();
        },
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 160,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              actions: [
                // Bouton de rafraîchissement
                IconButton(
                  icon: const Icon(Icons.refresh, color: Color(0xFF6366F1)),
                  tooltip: 'Actualiser',
                  onPressed: _refreshStatistics,
                ),
                // Bouton de déconnexion
                Consumer<AuthProvider>(
                  builder: (context, authProvider, _) {
                    return IconButton(
                      icon: const Icon(Icons.logout, color: Color(0xFF6366F1)),
                      tooltip: 'Déconnexion',
                      onPressed: () async {
                        // Afficher une boîte de dialogue de confirmation
                        final shouldLogout =
                            await showDialog<bool>(
                              context: context,
                              builder:
                                  (dialogContext) => AlertDialog(
                                    title: const Text('Déconnexion'),
                                    content: const Text(
                                      'Voulez-vous vraiment vous déconnecter ?',
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed:
                                            () => Navigator.of(
                                              dialogContext,
                                            ).pop(false),
                                        child: const Text('Annuler'),
                                      ),
                                      TextButton(
                                        onPressed:
                                            () => Navigator.of(
                                              dialogContext,
                                            ).pop(true),
                                        child: const Text('Déconnexion'),
                                      ),
                                    ],
                                  ),
                            ) ??
                            false;

                        if (shouldLogout) {
                          if (context.mounted) {
                            final navigator = Navigator.of(context);
                            await authProvider.logout();
                            // Rediriger vers l'écran de connexion en utilisant MaterialPageRoute
                            navigator.pushReplacement(
                              MaterialPageRoute(
                                builder: (_) => const LoginScreen(),
                              ),
                            );
                          }
                        }
                      },
                    );
                  },
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
                title: Row(
                  children: [
                    const VitaBrosseLogo(height: 32, showText: false),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'VitaBrosse®',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: Color(0xFF1F2937),
                              fontSize: 20,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Gestion Commerciale Professionnelle',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF6B7280),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF6366F1).withValues(alpha: 0.05),
                        const Color(0xFF8B5CF6).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 40,
                        right: 20,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF6366F1).withValues(alpha: 0.1),
                                const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 60,
                        right: 60,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF8B5CF6).withValues(alpha: 0.15),
                                const Color(0xFF6366F1).withValues(alpha: 0.15),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(
                  MediaQuery.of(context).size.width < 480 ? 16.0 : 20.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bienvenue !',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1F2937),
                        fontSize:
                            MediaQuery.of(context).size.width < 360
                                ? 20
                                : MediaQuery.of(context).size.width < 480
                                ? 22
                                : 24,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Voici un aperçu de votre activité commerciale',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.grey.shade600,
                        fontSize:
                            MediaQuery.of(context).size.width < 360
                                ? 13
                                : MediaQuery.of(context).size.width < 480
                                ? 14
                                : 16,
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                    ), // Statistics Cards - Responsive Layout
                    _buildResponsiveStatCards(context),

                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                    ),

                    // Revenue Chart Section
                    SectionHeader(
                      title: 'Analyse des revenus',
                      subtitle: 'Répartition du chiffre d\'affaires par client',
                    ),

                    const SizedBox(height: 16),

                    ProfessionalCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: const RevenueChart(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    Widget valueWidget,
  ) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildCardContent(String value, String subtitle) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isVerySmallScreen = screenWidth < 360;
        final isSmallScreen = screenWidth < 480;

        // Ajuster les tailles de police selon l'écran
        final valueFontSize =
            isVerySmallScreen ? 14.0 : (isSmallScreen ? 16.0 : 20.0);
        final subtitleFontSize =
            isVerySmallScreen ? 9.0 : (isSmallScreen ? 10.0 : 11.0);

        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment:
              MainAxisAlignment.center, // Centre le contenu verticalement
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: valueFontSize,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 1),
            Flexible(
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: subtitleFontSize,
                  color: Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildResponsiveStatCards(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 360; // iPhone SE et similaires
    final isSmallScreen = screenWidth < 480; // Petits téléphones
    final isMediumScreen = screenWidth < 600; // Grands téléphones

    final cards = [
      _buildStatCard(
        context,
        'Clients',
        Icons.people_outline,
        const Color(0xFF3B82F6),
        FutureBuilder<Map<String, dynamic>>(
          future: _clientStatsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const ModernLoadingIndicator(size: 16);
            }
            if (snapshot.hasError) {
              return _buildCardContent('0', 'Erreur');
            }
            if (snapshot.hasData && snapshot.data != null) {
              final data = snapshot.data!;
              return _buildCardContent('${data['totalClients'] ?? 0}', 'Total');
            }
            return _buildCardContent('0', 'Total');
          },
        ),
      ),
      _buildStatCard(
        context,
        'Produits',
        Icons.inventory_2_outlined,
        const Color(0xFF10B981),
        FutureBuilder<Map<String, dynamic>>(
          future: _productStatsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const ModernLoadingIndicator(size: 16);
            }
            if (snapshot.hasError) {
              return _buildCardContent('0', 'En stock');
            }
            if (snapshot.hasData && snapshot.data != null) {
              return _buildCardContent(
                '${snapshot.data!['nombreTotal'] ?? 0}',
                'En stock',
              );
            }
            return _buildCardContent('0', 'En stock');
          },
        ),
      ),
      _buildStatCard(
        context,
        'Commandes',
        Icons.shopping_cart_outlined,
        const Color(0xFFF59E0B),
        FutureBuilder<Map<String, dynamic>>(
          future: _orderStatsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const ModernLoadingIndicator(size: 16);
            }
            if (snapshot.hasError) {
              return _buildCardContent('0', 'Total');
            }
            if (snapshot.hasData && snapshot.data != null) {
              final data = snapshot.data!;
              return _buildCardContent('${data['total'] ?? 0}', 'Total');
            }
            return _buildCardContent('0', 'Total');
          },
        ),
      ),
      _buildStatCard(
        context,
        'Revenus',
        Icons.attach_money,
        const Color(0xFF8B5CF6),
        FutureBuilder<Map<String, dynamic>>(
          future: _orderStatsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const ModernLoadingIndicator(size: 16);
            }
            if (snapshot.hasError) {
              return _buildCardContent('0.0k DT', 'Total');
            }
            if (snapshot.hasData && snapshot.data != null) {
              final data = snapshot.data!;
              final ca = (data['chiffreAffaires'] as double?) ?? 0.0;
              return _buildCardContent(
                '${(ca / 1000).toStringAsFixed(1)}k DT',
                'Total',
              );
            }
            return _buildCardContent('0.0k DT', 'Total');
          },
        ),
      ),
    ];

    if (isMediumScreen) {
      // Sur écrans de téléphone : défilement horizontal optimisé
      final cardWidth =
          isVerySmallScreen ? 120.0 : (isSmallScreen ? 140.0 : 160.0);
      final cardHeight =
          isVerySmallScreen
              ? 110.0
              : (isSmallScreen
                  ? 120.0
                  : 130.0); // Augmenté pour éviter l'overflow

      return SizedBox(
        height: cardHeight,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: cards.length,
          separatorBuilder:
              (context, index) => SizedBox(width: isSmallScreen ? 8 : 12),
          itemBuilder: (context, index) {
            return SizedBox(width: cardWidth, child: cards[index]);
          },
        ),
      );
    } else {
      // Sur tablettes et écrans plus grands : grille responsive
      final crossAxisCount = screenWidth > 800 ? 4 : 2;
      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio:
            screenWidth > 800 ? 2.2 : 2.5, // Ajusté selon la largeur
        children: cards,
      );
    }
  }
}
