class Merchandiser {
  final int? id;
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String zone;
  final bool actif;
  final DateTime dateCreation;

  Merchandiser({
    this.id,
    required this.nom,
    required this.prenom,
    required this.email,
    required this.telephone,
    required this.zone,
    this.actif = true,
    required this.dateCreation,
  });

  // Convertir un Merchandiser en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'zone': zone,
      'actif': actif ? 1 : 0,
      'dateCreation': dateCreation.toIso8601String(),
    };
  }

  // Créer un Merchandiser à partir d'une Map de la base de données
  factory Merchandiser.fromMap(Map<String, dynamic> map) {
    return Merchandiser(
      id: map['id'],
      nom: map['nom'],
      prenom: map['prenom'],
      email: map['email'],
      telephone: map['telephone'],
      zone: map['zone'],
      actif: map['actif'] == 1,
      dateCreation: DateTime.parse(map['dateCreation']),
    );
  }

  // Créer une copie du merchandiser avec des modifications
  Merchandiser copyWith({
    int? id,
    String? nom,
    String? prenom,
    String? email,
    String? telephone,
    String? zone,
    bool? actif,
    DateTime? dateCreation,
  }) {
    return Merchandiser(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      zone: zone ?? this.zone,
      actif: actif ?? this.actif,
      dateCreation: dateCreation ?? this.dateCreation,
    );
  }

  // Nom complet du merchandiser
  String get nomComplet => '$prenom $nom';

  @override
  String toString() {
    return 'Merchandiser{id: $id, nom: $nom, prenom: $prenom, zone: $zone}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Merchandiser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
