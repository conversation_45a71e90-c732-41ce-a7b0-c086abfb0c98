import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_client_provider.dart';
import 'providers/produit_provider.dart';
import 'providers/commande_provider.dart';
import 'providers/merchandiser_provider.dart';
import 'providers/parcours_provider.dart';
import 'providers/catalogue_provider.dart';
import 'providers/tache_merchandising_provider.dart';
import 'providers/mission_provider.dart';
import 'providers/rapport_provider.dart';
import 'providers/devis_provider.dart';
import 'screens/auth/login_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/firebase_auth_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await FirebaseService.initializeFirestore();

    runApp(MyApp());
  } catch (e) {
    print('Erreur lors de l\'initialisation Firebase: $e');
    // En cas d'erreur, lancer l'app avec une interface d'erreur
    runApp(MyErrorApp(error: e.toString()));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseAuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseClientProvider()),
        ChangeNotifierProvider(create: (context) => ProduitProvider()),
        ChangeNotifierProvider(create: (context) => CommandeProvider()),
        ChangeNotifierProvider(create: (context) => DevisProvider()),
        ChangeNotifierProvider(create: (context) => MerchandiserProvider()),
        ChangeNotifierProvider(create: (context) => ParcoursProvider()),
        ChangeNotifierProvider(create: (context) => CatalogueProvider()),
        ChangeNotifierProvider(
          create: (context) => TacheMerchandisingProvider(),
        ),
        ChangeNotifierProvider(create: (context) => MissionProvider()),
        ChangeNotifierProvider(create: (context) => RapportProvider()),
      ],
      child: MaterialApp(
        title: 'VitaBrosse Pro',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
        home: const LoginScreen(),
      ),
    );
  }
}

class MyErrorApp extends StatelessWidget {
  final String error;

  const MyErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro - Erreur',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.red, useMaterial3: true),
      home: Scaffold(
        appBar: AppBar(
          title: Text('Erreur d\'initialisation'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 100, color: Colors.red),
                SizedBox(height: 20),
                Text(
                  'VitaBrosse Pro',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                Text(
                  'Erreur lors de l\'initialisation Firebase:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.red),
                ),
                SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // Relancer l'application
                    main();
                  },
                  child: Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
