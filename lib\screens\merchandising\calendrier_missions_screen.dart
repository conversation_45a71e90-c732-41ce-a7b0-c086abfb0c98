import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../models/mission.dart';
import 'creer_mission_calendrier_screen.dart';

class CalendrierMissionsScreen extends StatefulWidget {
  @override
  _CalendrierMissionsScreenState createState() =>
      _CalendrierMissionsScreenState();
}

class _CalendrierMissionsScreenState extends State<CalendrierMissionsScreen> {
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );

    // Simuler un ID de merchandiser - vous devrez adapter selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';

    missionProvider.chargerMissionsParMerchandiser(merchandiserId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Calendrier des missions',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerMissions,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: Consumer<MissionProvider>(
        builder: (context, missionProvider, child) {
          if (missionProvider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Chargement des missions...'),
                ],
              ),
            );
          }

          if (missionProvider.error != null) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                    const SizedBox(height: 16),
                    Text(
                      'Erreur de chargement',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      missionProvider.error!,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: _chargerMissions,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Réessayer'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          final missions = missionProvider.missions;

          return RefreshIndicator(
            onRefresh: () async => _chargerMissions(),
            child: Column(
              children: [
                // En-tête avec navigation du calendrier
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDate = DateTime(
                              _selectedDate.year,
                              _selectedDate.month - 1,
                              _selectedDate.day,
                            );
                          });
                        },
                        icon: const Icon(Icons.chevron_left),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                        ),
                      ),
                      Column(
                        children: [
                          Text(
                            _getMonthYearString(_selectedDate),
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${missions.length} mission${missions.length > 1 ? 's' : ''}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDate = DateTime(
                              _selectedDate.year,
                              _selectedDate.month + 1,
                              _selectedDate.day,
                            );
                          });
                        },
                        icon: const Icon(Icons.chevron_right),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                        ),
                      ),
                    ],
                  ),
                ),

                // Jours de la semaine
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 8,
                  ),
                  child: Row(
                    children:
                        ['L', 'M', 'M', 'J', 'V', 'S', 'D'].map((day) {
                          return Expanded(
                            child: Center(
                              child: Text(
                                day,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[700],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),

                // Calendrier
                Expanded(child: _buildCalendar(missions)),

                // Missions du jour sélectionné
                Container(height: 280, child: _buildMissionsDuJour(missions)),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _creerNouvelleMission(context),
        icon: const Icon(Icons.add),
        label: const Text('Nouvelle mission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        heroTag: "add_mission",
      ),
    );
  }

  Widget _buildCalendar(List<Mission> missions) {
    final firstDayOfMonth = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      1,
    );
    final startDate = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday - 1),
    );

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: 42, // 6 semaines * 7 jours
        itemBuilder: (context, index) {
          final date = startDate.add(Duration(days: index));
          final isCurrentMonth = date.month == _selectedDate.month;
          final isToday = _isSameDay(date, DateTime.now());
          final isSelected = _isSameDay(date, _selectedDate);

          // Trouver les missions pour cette date
          final missionsJour =
              missions
                  .where((mission) => _isSameDay(mission.dateEcheance, date))
                  .toList();

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });
            },
            onLongPress:
                isCurrentMonth
                    ? () => _creerNouvelleMission(context, date)
                    : null,
            child: Container(
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Colors.blue
                        : isToday
                        ? Colors.blue.shade50
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isCurrentMonth
                          ? (isSelected ? Colors.blue : Colors.grey.shade200)
                          : Colors.transparent,
                  width: isSelected ? 2 : 1,
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      date.day.toString(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight:
                            isToday || isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                        color:
                            isSelected
                                ? Colors.white
                                : isCurrentMonth
                                ? (isToday ? Colors.blue : Colors.black87)
                                : Colors.grey.shade400,
                      ),
                    ),
                  ),
                  if (missionsJour.isNotEmpty)
                    Positioned(
                      right: 6,
                      top: 6,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: _getMissionDotColor(missionsJour),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  if (missionsJour.length > 1)
                    Positioned(
                      right: 14,
                      top: 6,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: _getMissionDotColor(
                            missionsJour,
                          ).withOpacity(0.7),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMissionsDuJour(List<Mission> missions) {
    final missionsJour =
        missions
            .where((mission) => _isSameDay(mission.dateEcheance, _selectedDate))
            .toList();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Handle pour indiquer que c'est scrollable
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Missions du ${_selectedDate.day}/${_selectedDate.month}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${missionsJour.length} mission${missionsJour.length > 1 ? 's' : ''}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed:
                      () => _creerNouvelleMission(context, _selectedDate),
                  icon: const Icon(Icons.add_circle),
                  color: Colors.blue,
                  iconSize: 28,
                  tooltip: 'Ajouter une mission',
                ),
              ],
            ),
          ),

          Expanded(
            child:
                missionsJour.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.event_available,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Aucune mission pour ce jour',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextButton.icon(
                            onPressed:
                                () => _creerNouvelleMission(
                                  context,
                                  _selectedDate,
                                ),
                            icon: const Icon(Icons.add),
                            label: const Text('Créer une mission'),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: missionsJour.length,
                      itemBuilder: (context, index) {
                        final mission = missionsJour[index];
                        return _buildMissionItem(mission);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildMissionItem(Mission mission) {
    final prioriteColor = _getPrioriteColor(mission.priorite);
    final statutColor = _getStatutColor(mission.statut);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          _afficherDetailsMission(mission);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 50,
                decoration: BoxDecoration(
                  color: prioriteColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      mission.titre,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                        decoration:
                            mission.estTerminee
                                ? TextDecoration.lineThrough
                                : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      mission.clientNom,
                      style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                    ),
                    if (mission.taches.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${mission.taches.length} tâche${mission.taches.length > 1 ? 's' : ''}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statutColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      mission.statutAffichage,
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: statutColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: prioriteColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      mission.priorite.toUpperCase(),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: prioriteColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Nouvelles méthodes
  void _creerNouvelleMission(BuildContext context, [DateTime? date]) async {
    final selectedDate = date ?? _selectedDate;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                CreerMissionCalendrierScreen(selectedDate: selectedDate),
      ),
    );

    if (result == true) {
      // Recharger les missions si une nouvelle mission a été créée
      _chargerMissions();
    }
  }

  void _afficherDetailsMission(Mission mission) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  height: 4,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              mission.titre,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatutColor(
                                mission.statut,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Text(
                              mission.statutAffichage,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: _getStatutColor(mission.statut),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        mission.description,
                        style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                      ),
                      const SizedBox(height: 20),
                      _buildDetailRow('Client', mission.clientNom, Icons.store),
                      _buildDetailRow(
                        'Priorité',
                        mission.priorite,
                        Icons.priority_high,
                      ),
                      _buildDetailRow(
                        'Date d\'échéance',
                        '${mission.dateEcheance.day}/${mission.dateEcheance.month}/${mission.dateEcheance.year}',
                        Icons.calendar_today,
                      ),
                      if (mission.taches.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Tâches à accomplir',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...mission.taches.map(
                          (tache) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 8),
                                Expanded(child: Text(tache)),
                              ],
                            ),
                          ),
                        ),
                      ],
                      if (mission.notes != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Notes',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          mission.notes!,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[600])),
          ),
        ],
      ),
    );
  }

  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return Colors.red;
      case 'haute':
        return Colors.orange;
      case 'normale':
        return Colors.blue;
      case 'faible':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  Color _getMissionDotColor(List<Mission> missions) {
    if (missions.any((m) => m.estEnRetard)) {
      return Colors.red;
    } else if (missions.any((m) => m.priorite == 'urgente')) {
      return Colors.orange;
    } else if (missions.any((m) => m.statut == 'en_cours')) {
      return Colors.blue;
    } else if (missions.any((m) => m.statut == 'terminee')) {
      return Colors.green;
    }
    return Colors.grey;
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange;
      case 'en_cours':
        return Colors.blue;
      case 'terminee':
        return Colors.green;
      case 'annulee':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
