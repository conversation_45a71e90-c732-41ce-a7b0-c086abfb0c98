import 'package:flutter/material.dart';
import '../models/catalogue.dart';
import '../services/catalogue_service.dart';

class CatalogueProvider with ChangeNotifier {
  List<Catalogue> _catalogues = [];
  List<Catalogue> _cataloguesRecents = [];
  bool _isLoading = false;
  String? _error;
  TypeCatalogue? _filtreType;

  List<Catalogue> get catalogues => _catalogues;
  List<Catalogue> get cataloguesRecents => _cataloguesRecents;
  bool get isLoading => _isLoading;
  String? get error => _error;
  TypeCatalogue? get filtreType => _filtreType;

  // Obtenir les catalogues filtrés
  List<Catalogue> get cataloguesFiltres {
    if (_filtreType == null) {
      return _catalogues;
    }
    return _catalogues.where((cat) => cat.type == _filtreType).toList();
  }

  // Charger tous les catalogues
  Future<void> chargerCatalogues() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _catalogues = await CatalogueService.obtenirTousLesCatalogues();
      _error = null;
      // Charger aussi les catalogues récents
      await chargerCataloguesRecents();
    } catch (e) {
      _error = 'Erreur lors du chargement des catalogues: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les catalogues récents
  Future<void> chargerCataloguesRecents() async {
    try {
      _cataloguesRecents = await CatalogueService.obtenirCataloguesRecents();
      notifyListeners();
    } catch (e) {
      print('Erreur lors du chargement des catalogues récents: $e');
    }
  }

  // Filtrer par type
  void filtrerParType(TypeCatalogue? type) {
    _filtreType = type;
    notifyListeners();
  }

  // Rechercher dans les catalogues
  Future<void> rechercherCatalogues(String terme) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      if (terme.isEmpty) {
        await chargerCatalogues();
      } else {
        _catalogues = await CatalogueService.rechercherCatalogues(terme);
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau catalogue
  Future<bool> creerCatalogue(Catalogue catalogue) async {
    try {
      await CatalogueService.creerCatalogue(catalogue);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour un catalogue
  Future<bool> mettreAJourCatalogue(String id, Catalogue catalogue) async {
    try {
      await CatalogueService.mettreAJourCatalogue(id, catalogue);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un catalogue
  Future<bool> supprimerCatalogue(String id) async {
    try {
      await CatalogueService.supprimerCatalogue(id);
      await chargerCatalogues();
      await chargerCataloguesRecents();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du catalogue: $e';
      notifyListeners();
      return false;
    }
  }

  // Obtenir un catalogue par ID
  Future<Catalogue?> obtenirCatalogueParId(String id) async {
    try {
      return await CatalogueService.obtenirCatalogueParId(id);
    } catch (e) {
      _error = 'Erreur lors de la récupération du catalogue: $e';
      notifyListeners();
      return null;
    }
  }

  // Effacer les erreurs
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Actualiser les données
  Future<void> actualiser() async {
    await Future.wait([chargerCatalogues(), chargerCataloguesRecents()]);
  }
}
