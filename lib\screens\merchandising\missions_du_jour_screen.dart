import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/mission.dart';
import '../../providers/mission_provider.dart';
// import 'detail_mission_screen.dart';

class MissionsDuJourScreen extends StatefulWidget {
  const MissionsDuJourScreen({super.key});

  @override
  State<MissionsDuJourScreen> createState() => _MissionsDuJourScreenState();
}

class _MissionsDuJourScreenState extends State<MissionsDuJourScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    missionProvider.chargerMissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Missions du jour'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerMissions,
          ),
        ],
      ),
      body: Consumer<MissionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final missionsDuJour = provider.missionsDuJour;
          final missionsEnRetard = provider.missionsEnRetard;

          return RefreshIndicator(
            onRefresh: () async => _chargerMissions(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Résumé
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'Missions du jour',
                          missionsDuJour.length.toString(),
                          Icons.today,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildStatCard(
                          'En retard',
                          missionsEnRetard.length.toString(),
                          Icons.schedule,
                          Colors.red,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Missions en retard
                  if (missionsEnRetard.isNotEmpty) ...[
                    const Text(
                      'Missions en retard',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...missionsEnRetard.map(
                      (mission) => _buildMissionCard(mission, true),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Missions du jour
                  const Text(
                    'Missions d\'aujourd\'hui',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  if (missionsDuJour.isEmpty)
                    Center(
                      child: Column(
                        children: [
                          const SizedBox(height: 32),
                          Icon(
                            Icons.assignment_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucune mission pour aujourd\'hui',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    ...missionsDuJour.map(
                      (mission) => _buildMissionCard(mission, false),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionCard(Mission mission, bool isEnRetard) {
    Color prioriteColor;
    switch (mission.priorite) {
      case 'urgente':
        prioriteColor = Colors.red;
        break;
      case 'haute':
        prioriteColor = Colors.orange;
        break;
      case 'normale':
        prioriteColor = Colors.blue;
        break;
      case 'faible':
        prioriteColor = Colors.green;
        break;
      default:
        prioriteColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          color: isEnRetard ? Colors.red : prioriteColor,
        ),
        title: Text(
          mission.titre,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(mission.clientNom),
            const SizedBox(height: 4),
            Text(
              mission.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.flag, size: 16, color: prioriteColor),
                const SizedBox(width: 4),
                Text(
                  mission.priorite.toUpperCase(),
                  style: TextStyle(
                    color: prioriteColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: isEnRetard ? Colors.red : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${mission.dateEcheance.day}/${mission.dateEcheance.month}',
                  style: TextStyle(
                    color: isEnRetard ? Colors.red : Colors.grey[600],
                    fontSize: 12,
                    fontWeight:
                        isEnRetard ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getStatutColor(mission.statut).withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatutText(mission.statut),
            style: TextStyle(
              color: _getStatutColor(mission.statut),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () {
          // Navigation vers les détails - fonctionnalité en cours de développement
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Détails de la mission - Fonctionnalité en cours de développement',
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'termine':
        return Colors.green;
      case 'en_cours':
        return Colors.orange;
      case 'en_attente':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'termine':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      default:
        return 'Inconnu';
    }
  }
}
