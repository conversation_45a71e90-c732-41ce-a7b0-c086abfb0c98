import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/catalogue.dart';
import '../../providers/catalogue_provider.dart';
import '../../widgets/professional_ui_components.dart';

class CatalogueFormScreen extends StatefulWidget {
  final Catalogue? catalogue;

  const CatalogueFormScreen({Key? key, this.catalogue}) : super(key: key);

  @override
  State<CatalogueFormScreen> createState() => _CatalogueFormScreenState();
}

class _CatalogueFormScreenState extends State<CatalogueFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _urlPdfController = TextEditingController();

  TypeCatalogue _selectedType = TypeCatalogue.produits;
  bool _isLoading = false;
  bool get _isEditing => widget.catalogue != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _nomController.text = widget.catalogue!.nom;
      _descriptionController.text = widget.catalogue!.description;
      _urlPdfController.text = widget.catalogue!.urlPdf ?? '';
      _selectedType = widget.catalogue!.type;
    }
  }

  @override
  void dispose() {
    _nomController.dispose();
    _descriptionController.dispose();
    _urlPdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Modifier le catalogue' : 'Nouveau catalogue'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1F2937),
        elevation: 0,
        surfaceTintColor: Colors.white,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.only(right: 16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SectionHeader(
                title: 'Informations générales',
                subtitle: 'Renseignez les détails du catalogue',
              ),
              const SizedBox(height: 20),

              // Nom du catalogue
              TextFormField(
                controller: _nomController,
                decoration: const InputDecoration(
                  labelText: 'Nom du catalogue *',
                  hintText: 'Ex: Catalogue VitaBrosse 2024',
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Le nom est requis';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Décrivez le contenu du catalogue',
                  prefixIcon: Icon(Icons.description),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'La description est requise';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Type de catalogue
              DropdownButtonFormField<TypeCatalogue>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Type de catalogue *',
                  prefixIcon: Icon(Icons.category),
                ),
                items:
                    TypeCatalogue.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Row(
                          children: [
                            Text(
                              _getIconForType(type),
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 12),
                            Text(_getLabelForType(type)),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 30),

              // Section URL PDF
              const SectionHeader(
                title: 'Lien PDF',
                subtitle: 'Entrez l\'URL du fichier PDF du catalogue',
              ),
              const SizedBox(height: 20),

              // URL PDF
              TextFormField(
                controller: _urlPdfController,
                decoration: const InputDecoration(
                  labelText: 'URL du fichier PDF',
                  hintText: 'https://exemple.com/catalogue.pdf',
                  prefixIcon: Icon(Icons.link),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!Uri.tryParse(value)!.isAbsolute) {
                      return 'Veuillez entrer une URL valide';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: 20),

              // Informations d'aide
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF3B82F6).withOpacity(0.2),
                  ),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Color(0xFF3B82F6)),
                        SizedBox(width: 8),
                        Text(
                          'Comment ajouter un catalogue PDF ?',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF3B82F6),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '1. Uploadez votre fichier PDF sur un service comme Google Drive, Dropbox ou votre serveur\n'
                      '2. Obtenez le lien public de téléchargement\n'
                      '3. Copiez et collez ce lien dans le champ ci-dessus\n'
                      '4. Les utilisateurs pourront consulter le catalogue directement dans l\'application',
                      style: TextStyle(color: Color(0xFF6B7280), fontSize: 14),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 40),

              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.pop(context),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _sauvegarder,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF10B981),
                        foregroundColor: Colors.white,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(_isEditing ? 'Modifier' : 'Créer'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _sauvegarder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Créer ou mettre à jour le catalogue
      final catalogue = Catalogue(
        id: _isEditing ? widget.catalogue!.id : '',
        nom: _nomController.text,
        description: _descriptionController.text,
        type: _selectedType,
        urlPdf:
            _urlPdfController.text.isNotEmpty ? _urlPdfController.text : null,
        dateCreation:
            _isEditing ? widget.catalogue!.dateCreation : DateTime.now(),
        dateModification: DateTime.now(),
      );

      bool success;
      if (_isEditing) {
        success = await context.read<CatalogueProvider>().mettreAJourCatalogue(
          widget.catalogue!.id,
          catalogue,
        );
      } else {
        success = await context.read<CatalogueProvider>().creerCatalogue(
          catalogue,
        );
      }

      if (success) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing
                  ? 'Catalogue modifié avec succès'
                  : 'Catalogue créé avec succès',
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<CatalogueProvider>().error ?? 'Erreur inconnue',
            ),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getIconForType(TypeCatalogue type) {
    switch (type) {
      case TypeCatalogue.produits:
        return '📦';
      case TypeCatalogue.promotions:
        return '🎯';
      case TypeCatalogue.nouveautes:
        return '✨';
      case TypeCatalogue.documentation:
        return '📚';
      case TypeCatalogue.formation:
        return '🎓';
    }
  }

  String _getLabelForType(TypeCatalogue type) {
    switch (type) {
      case TypeCatalogue.produits:
        return 'Catalogue Produits';
      case TypeCatalogue.promotions:
        return 'Catalogue Promotions';
      case TypeCatalogue.nouveautes:
        return 'Catalogue Nouveautés';
      case TypeCatalogue.documentation:
        return 'Documentation';
      case TypeCatalogue.formation:
        return 'Formation';
    }
  }
}
