import '../models/catalogue.dart';
import '../services/catalogue_service.dart';

class CatalogueInitializer {
  static Future<void> initializeSampleCatalogues() async {
    // Vérifier si des catalogues existent déjà
    final existingCatalogues =
        await CatalogueService.obtenirTousLesCatalogues();
    if (existingCatalogues.isNotEmpty) {
      return; // Ne pas créer de samples s'il y en a déjà
    }

    // Créer des catalogues d'exemple
    final sampleCatalogues = [
      Catalogue(
        id: '',
        nom: 'Catalogue VitaBrosse 2024',
        description:
            'Catalogue complet de nos produits VitaBrosse pour l\'année 2024. Découvrez toute notre gamme de brosses à dents électriques innovantes.',
        type: TypeCatalogue.produits,
        urlPdf:
            'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        dateCreation: DateTime.now().subtract(const Duration(days: 30)),
        dateModification: DateTime.now().subtract(const Duration(days: 5)),
      ),
      Catalogue(
        id: '',
        nom: 'Promotions Printemps 2024',
        description:
            'Offres spéciales et promotions exceptionnelles sur notre gamme VitaBrosse. Valable du 15 mars au 30 avril 2024.',
        type: TypeCatalogue.promotions,
        urlPdf:
            'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        dateCreation: DateTime.now().subtract(const Duration(days: 15)),
        dateModification: DateTime.now().subtract(const Duration(days: 2)),
      ),
      Catalogue(
        id: '',
        nom: 'Nouveautés Innovation 2024',
        description:
            'Découvrez nos dernières innovations technologiques en matière de soins dentaires. Modèles exclusifs et technologies de pointe.',
        type: TypeCatalogue.nouveautes,
        urlPdf:
            'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        dateCreation: DateTime.now().subtract(const Duration(days: 10)),
        dateModification: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Catalogue(
        id: '',
        nom: 'Guide d\'utilisation VitaBrosse',
        description:
            'Manuel d\'utilisation complet et conseils d\'entretien pour optimiser l\'utilisation de votre brosse à dents VitaBrosse.',
        type: TypeCatalogue.documentation,
        urlPdf:
            'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        dateCreation: DateTime.now().subtract(const Duration(days: 60)),
        dateModification: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Catalogue(
        id: '',
        nom: 'Formation Commerciale 2024',
        description:
            'Programme de formation pour les équipes commerciales. Techniques de vente et connaissances produits avancées.',
        type: TypeCatalogue.formation,
        urlPdf:
            'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        dateCreation: DateTime.now().subtract(const Duration(days: 45)),
        dateModification: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];

    // Créer chaque catalogue
    for (final catalogue in sampleCatalogues) {
      try {
        await CatalogueService.creerCatalogue(catalogue);
        print('Catalogue créé: ${catalogue.nom}');
      } catch (e) {
        print('Erreur lors de la création du catalogue ${catalogue.nom}: $e');
      }
    }
  }
}
