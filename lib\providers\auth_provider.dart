import 'package:flutter/foundation.dart';
import 'firebase_auth_provider.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseAuthProvider _firebaseAuthProvider = FirebaseAuthProvider();

  String? _userType;

  bool get isAuthenticated => _firebaseAuthProvider.isAuthenticated;
  bool get isLoading => _firebaseAuthProvider.isLoading;
  String? get currentUser => _firebaseAuthProvider.user?.email;
  String? get userType => _userType;
  String? get error => _firebaseAuthProvider.errorMessage;

  AuthProvider() {
    // Écouter les changements du FirebaseAuthProvider
    _firebaseAuthProvider.addListener(_onFirebaseAuthChange);
  }

  void _onFirebaseAuthChange() {
    notifyListeners();
  }

  @override
  void dispose() {
    _firebaseAuthProvider.removeListener(_onFirebaseAuthChange);
    super.dispose();
  }

  // Méthode de connexion utilisant Firebase
  Future<bool> login(String email, String password) async {
    final success = await _firebaseAuthProvider.signInWithEmailAndPassword(
      email,
      password,
    );

    if (success) {
      // Récupérer le type d'utilisateur après connexion réussie
      final profile = await _firebaseAuthProvider.getUserProfile();
      _userType = profile?['userType'] as String?;
      notifyListeners();
    }

    return success;
  }

  // Méthode d'inscription utilisant Firebase
  Future<bool> signup({
    required String nomComplet,
    required String email,
    required String password,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
    String status = 'actif', // Changé pour les tests
  }) async {
    return await _firebaseAuthProvider.signUpWithUserData(
      email: email,
      password: password,
      nomComplet: nomComplet,
      telephone: telephone,
      userType: userType,
      mobile: mobile,
      territoire: territoire,
      status: status,
    );
  }

  // Déconnexion
  Future<void> logout() async {
    await _firebaseAuthProvider.signOut();
    _userType = null;
    notifyListeners();
  }

  // Continuer en tant qu'invité (si nécessaire)
  void loginAsGuest() {
    // Cette méthode peut être supprimée si vous n'utilisez pas le mode invité
  }

  // Vérifier le statut d'authentification
  Future<void> checkAuthStatus() async {
    // Le FirebaseAuthProvider gère automatiquement l'état d'authentification
    notifyListeners();
  }

  void clearError() {
    _firebaseAuthProvider.clearError();
  }
}
