import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'login_screen_professional.dart';

class SignupUnifiedNewScreen extends StatefulWidget {
  const SignupUnifiedNewScreen({super.key});

  @override
  State<SignupUnifiedNewScreen> createState() => _SignupUnifiedNewScreenState();
}

class _SignupUnifiedNewScreenState extends State<SignupUnifiedNewScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomCompletController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _mobileController = TextEditingController();
  final _territoireController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  String _selectedUserType = 'commercial'; // Default to commercial
  bool _acceptTerms = false;

  @override
  void dispose() {
    _nomCompletController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _telephoneController.dispose();
    _mobileController.dispose();
    _territoireController.dispose();
    super.dispose();
  }

  Future<void> _handleSignup() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_acceptTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez accepter les conditions d\'utilisation'),
          backgroundColor: Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.signup(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        nomComplet: _nomCompletController.text.trim(),
        telephone: _telephoneController.text.trim(),
        userType: _selectedUserType,
        mobile:
            _mobileController.text.trim().isNotEmpty
                ? _mobileController.text.trim()
                : null,
        territoire:
            _territoireController.text.trim().isNotEmpty
                ? _territoireController.text.trim()
                : null,
        status: 'inactif',
      );

      if (mounted && success) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Compte créé avec succès ! Vous pouvez maintenant vous connecter.',
            ),
            backgroundColor: Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else if (mounted && authProvider.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error!),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: Icon(prefixIcon),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFEF4444), width: 2),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildUserTypeSelector() {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type de compte',
          style: TextStyle(
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1E293B),
          ),
        ),
        SizedBox(height: isSmallScreen ? 8 : 12),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _selectedUserType = 'commercial'),
                child: Container(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                  decoration: BoxDecoration(
                    color:
                        _selectedUserType == 'commercial'
                            ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                            : Colors.grey.shade50,
                    border: Border.all(
                      color:
                          _selectedUserType == 'commercial'
                              ? const Color(0xFF3B82F6)
                              : Colors.grey.shade300,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.business_center,
                        size: isSmallScreen ? 24 : 28,
                        color:
                            _selectedUserType == 'commercial'
                                ? const Color(0xFF3B82F6)
                                : Colors.grey.shade600,
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 6),
                      Text(
                        'Commercial',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: isSmallScreen ? 13 : 14,
                          color:
                              _selectedUserType == 'commercial'
                                  ? const Color(0xFF3B82F6)
                                  : Colors.grey.shade700,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      Text(
                        'Gestion clients\net commandes',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 10 : 11,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _selectedUserType = 'merchandiser'),
                child: Container(
                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                  decoration: BoxDecoration(
                    color:
                        _selectedUserType == 'merchandiser'
                            ? const Color(0xFF10B981).withValues(alpha: 0.1)
                            : Colors.grey.shade50,
                    border: Border.all(
                      color:
                          _selectedUserType == 'merchandiser'
                              ? const Color(0xFF10B981)
                              : Colors.grey.shade300,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.store,
                        size: isSmallScreen ? 24 : 28,
                        color:
                            _selectedUserType == 'merchandiser'
                                ? const Color(0xFF10B981)
                                : Colors.grey.shade600,
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 6),
                      Text(
                        'Merchandiser',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: isSmallScreen ? 13 : 14,
                          color:
                              _selectedUserType == 'merchandiser'
                                  ? const Color(0xFF10B981)
                                  : Colors.grey.shade700,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 2 : 4),
                      Text(
                        'Missions\nterrain',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 10 : 11,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 16.0 : 24.0,
                vertical: isSmallScreen ? 20.0 : 32.0,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isSmallScreen ? double.infinity : 500,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Logo et titre section
                    ProfessionalCard(
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
                        child: Column(
                          children: [
                            VitaBrosseLogo(height: isSmallScreen ? 40 : 50),
                            SizedBox(height: isSmallScreen ? 12 : 16),
                            Text(
                              'Créer un compte',
                              style: Theme.of(
                                context,
                              ).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: const Color(0xFF1E293B),
                                fontSize: isSmallScreen ? 20 : 24,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Rejoignez VitaBrosse Pro',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color: const Color(0xFF64748B),
                                fontSize: isSmallScreen ? 13 : 15,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: isSmallScreen ? 12 : 16),

                    // Formulaire d'inscription
                    ProfessionalCard(
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
                        child: Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            return Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // User type selector
                                  _buildUserTypeSelector(),

                                  SizedBox(height: isSmallScreen ? 16 : 20),

                                  // Nom complet
                                  TextFormField(
                                    controller: _nomCompletController,
                                    decoration: InputDecoration(
                                      labelText: 'Nom complet',
                                      hintText: 'Votre nom et prénom',
                                      prefixIcon: const Icon(
                                        Icons.person_outline,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                          color: Colors.grey.shade300,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: const BorderSide(
                                          color: Color(0xFF3B82F6),
                                          width: 2,
                                        ),
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Veuillez saisir votre nom complet';
                                      }
                                      return null;
                                    },
                                  ),

                                  SizedBox(height: isSmallScreen ? 12 : 16),

                                  // Email
                                  _buildFormField(
                                    controller: _emailController,
                                    labelText: 'Adresse email',
                                    hintText: '<EMAIL>',
                                    prefixIcon: Icons.email_outlined,
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Veuillez saisir votre email';
                                      }
                                      if (!RegExp(
                                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                      ).hasMatch(value)) {
                                        return 'Format d\'email invalide';
                                      }
                                      return null;
                                    },
                                  ),

                                  SizedBox(height: isSmallScreen ? 12 : 16),

                                  // Téléphone
                                  _buildFormField(
                                    controller: _telephoneController,
                                    labelText: 'Téléphone',
                                    hintText: '+216 XX XXX XXX',
                                    prefixIcon: Icons.phone_outlined,
                                    keyboardType: TextInputType.phone,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Veuillez saisir votre numéro de téléphone';
                                      }
                                      return null;
                                    },
                                  ),

                                  SizedBox(height: isSmallScreen ? 12 : 16),

                                  // Mobile (optionnel)
                                  _buildFormField(
                                    controller: _mobileController,
                                    labelText: 'Mobile (optionnel)',
                                    hintText: '+216 XX XXX XXX',
                                    prefixIcon: Icons.smartphone_outlined,
                                    keyboardType: TextInputType.phone,
                                  ),

                                  if (_selectedUserType == 'merchandiser') ...[
                                    SizedBox(height: isSmallScreen ? 12 : 16),
                                    // Territoire (pour merchandiser)
                                    _buildFormField(
                                      controller: _territoireController,
                                      labelText: 'Territoire',
                                      hintText: 'Zone géographique',
                                      prefixIcon: Icons.location_on_outlined,
                                    ),
                                  ],

                                  SizedBox(height: isSmallScreen ? 12 : 16),

                                  // Mot de passe
                                  _buildFormField(
                                    controller: _passwordController,
                                    labelText: 'Mot de passe',
                                    hintText: 'Minimum 6 caractères',
                                    prefixIcon: Icons.lock_outline,
                                    obscureText: !_isPasswordVisible,
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: const Color(0xFF64748B),
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Veuillez saisir un mot de passe';
                                      }
                                      if (value.length < 6) {
                                        return 'Le mot de passe doit contenir au moins 6 caractères';
                                      }
                                      return null;
                                    },
                                  ),

                                  SizedBox(height: isSmallScreen ? 12 : 16),

                                  // Confirmer mot de passe
                                  _buildFormField(
                                    controller: _confirmPasswordController,
                                    labelText: 'Confirmer le mot de passe',
                                    hintText: 'Retapez votre mot de passe',
                                    prefixIcon: Icons.lock_outline,
                                    obscureText: !_isConfirmPasswordVisible,
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isConfirmPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: const Color(0xFF64748B),
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isConfirmPasswordVisible =
                                              !_isConfirmPasswordVisible;
                                        });
                                      },
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Veuillez confirmer votre mot de passe';
                                      }
                                      if (value != _passwordController.text) {
                                        return 'Les mots de passe ne correspondent pas';
                                      }
                                      return null;
                                    },
                                  ),

                                  const SizedBox(height: 20),

                                  // Accepter les conditions
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Checkbox(
                                        value: _acceptTerms,
                                        onChanged: (value) {
                                          setState(() {
                                            _acceptTerms = value ?? false;
                                          });
                                        },
                                        activeColor: const Color(0xFF3B82F6),
                                      ),
                                      Expanded(
                                        child: Text(
                                          'J\'accepte les conditions d\'utilisation et la politique de confidentialité',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 24),

                                  // Bouton d'inscription
                                  SizedBox(
                                    width: double.infinity,
                                    height: 50,
                                    child: ElevatedButton(
                                      onPressed:
                                          authProvider.isLoading
                                              ? null
                                              : _handleSignup,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(
                                          0xFF3B82F6,
                                        ),
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        elevation: 2,
                                      ),
                                      child:
                                          authProvider.isLoading
                                              ? const SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                        Color
                                                      >(Colors.white),
                                                ),
                                              )
                                              : const Text(
                                                'Créer mon compte',
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Lien vers la connexion
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Text(
                                        'Vous avez déjà un compte ? ',
                                        style: TextStyle(
                                          color: Color(0xFF64748B),
                                          fontSize: 14,
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pushReplacement(
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      const LoginScreen(),
                                            ),
                                          );
                                        },
                                        child: const Text(
                                          'Se connecter',
                                          style: TextStyle(
                                            color: Color(0xFF3B82F6),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
