import '../database/database_helper.dart';
import '../models/merchandiser.dart';

class MerchandiserService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Obtenir tous les merchandisers
  Future<List<Merchandiser>> obtenirTousMerchandisers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('merchandisers');
    return List.generate(maps.length, (i) {
      return Merchandiser.fromMap(maps[i]);
    });
  }

  // Ajouter un merchandiser
  Future<int> ajouterMerchandiser(Merchandiser merchandiser) async {
    final db = await _databaseHelper.database;
    return await db.insert('merchandisers', merchandiser.toMap());
  }

  // Modifier un merchandiser
  Future<bool> modifierMerchandiser(Merchandiser merchandiser) async {
    final db = await _databaseHelper.database;
    final result = await db.update(
      'merchandisers',
      merchandiser.toMap(),
      where: 'id = ?',
      whereArgs: [merchandiser.id],
    );
    return result > 0;
  }

  // Supprimer un merchandiser
  Future<bool> supprimerMerchandiser(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.delete(
      'merchandisers',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  // Rechercher des merchandisers
  Future<List<Merchandiser>> rechercherMerchandisers(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'merchandisers',
      where: 'nom LIKE ? OR prenom LIKE ? OR email LIKE ? OR zone LIKE ?',
      whereArgs: ['%$terme%', '%$terme%', '%$terme%', '%$terme%'],
    );
    return List.generate(maps.length, (i) {
      return Merchandiser.fromMap(maps[i]);
    });
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    final db = await _databaseHelper.database;

    final nombreTotal = await db.rawQuery(
      'SELECT COUNT(*) as count FROM merchandisers',
    );
    final nombreActifs = await db.rawQuery(
      'SELECT COUNT(*) as count FROM merchandisers WHERE actif = 1',
    );
    final nombreInactifs = await db.rawQuery(
      'SELECT COUNT(*) as count FROM merchandisers WHERE actif = 0',
    );

    return {
      'nombreTotal': nombreTotal.first['count'],
      'nombreActifs': nombreActifs.first['count'],
      'nombreInactifs': nombreInactifs.first['count'],
    };
  }

  // Obtenir un merchandiser par ID
  Future<Merchandiser?> obtenirMerchandiserParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'merchandisers',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Merchandiser.fromMap(maps.first);
    }
    return null;
  }

  // Obtenir les merchandisers actifs
  Future<List<Merchandiser>> obtenirMerchandisersActifs() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'merchandisers',
      where: 'actif = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) {
      return Merchandiser.fromMap(maps[i]);
    });
  }

  // Obtenir les merchandisers par zone
  Future<List<Merchandiser>> obtenirMerchandisersParZone(String zone) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'merchandisers',
      where: 'zone = ? AND actif = ?',
      whereArgs: [zone, 1],
    );
    return List.generate(maps.length, (i) {
      return Merchandiser.fromMap(maps[i]);
    });
  }
}
