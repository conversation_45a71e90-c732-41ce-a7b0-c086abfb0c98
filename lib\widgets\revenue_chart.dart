import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../providers/firebase_client_provider.dart';
import '../providers/commande_provider.dart';
import 'professional_ui_components.dart';

class RevenueChart extends StatefulWidget {
  const RevenueChart({super.key});

  @override
  State<RevenueChart> createState() => _RevenueChartState();
}

class _RevenueChartState extends State<RevenueChart> {
  Future<Map<String, dynamic>>? _revenueDataFuture;

  @override
  void initState() {
    super.initState();
    _initializeRevenueDataWithLoading();
  }

  Future<void> _initializeRevenueDataWithLoading() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final commandeProvider = Provider.of<CommandeProvider>(
      context,
      listen: false,
    );

    try {
      // Ensure base data is loaded first
      await Future.wait([
        clientProvider.loadClients(),
        commandeProvider.chargerCommandes(),
      ]);

      _revenueDataFuture = _getRevenueData(clientProvider, commandeProvider);

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error initializing revenue chart data: $e');
      // Initialize with empty future to prevent null errors
      _revenueDataFuture = _getRevenueData(clientProvider, commandeProvider);
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _revenueDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: ModernLoadingIndicator(size: 24));
        }

        if (snapshot.hasError) {
          return _buildErrorWidget(context);
        }

        if (!snapshot.hasData || snapshot.data!['chartData'].isEmpty) {
          return _buildEmptyWidget(context);
        }

        final data = snapshot.data!;
        return _buildChartWidget(context, data);
      },
    );
  }

  Future<Map<String, dynamic>> _getRevenueData(
    FirebaseClientProvider clientProvider,
    CommandeProvider commandeProvider,
  ) async {
    try {
      // Charger les clients et commandes
      await clientProvider.loadClients();
      await commandeProvider.chargerCommandes();

      final clients = clientProvider.clients;
      final commandes = commandeProvider.commandes;

      // Calculer le chiffre d'affaires par client
      Map<String, double> clientRevenue = {};
      Map<String, String> clientNames = {};

      for (final client in clients) {
        final clientId = client.id ?? '';
        final clientName = client.nomClient ?? client.nom ?? 'Client inconnu';
        if (clientId.isNotEmpty) {
          clientNames[clientId] = clientName;
          clientRevenue[clientId] = 0.0;
        }
      }

      for (final commande in commandes) {
        if (commande.statut.name != 'annulee' &&
            clientRevenue.containsKey(commande.clientId)) {
          clientRevenue[commande.clientId] =
              (clientRevenue[commande.clientId] ?? 0.0) + commande.montantTotal;
        }
      }

      // Trier les clients par chiffre d'affaires décroissant
      final sortedClients =
          clientRevenue.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      // Prendre les 5 meilleurs clients + regrouper les autres
      final topClients = sortedClients.take(4).toList();
      final othersRevenue = sortedClients
          .skip(4)
          .fold(0.0, (sum, entry) => sum + entry.value);

      final totalRevenue = sortedClients.fold(
        0.0,
        (sum, entry) => sum + entry.value,
      );

      if (totalRevenue == 0) {
        return {'chartData': [], 'totalRevenue': 0.0, 'bestClient': null};
      }

      List<PieChartSectionData> chartData = [];
      final colors = [
        const Color(0xFF6366F1), // Indigo
        const Color(0xFF8B5CF6), // Purple
        const Color(0xFF10B981), // Emerald
        const Color(0xFFF59E0B), // Amber
        const Color(0xFFEF4444), // Red
      ];

      // Ajouter les top clients
      for (int i = 0; i < topClients.length; i++) {
        final client = topClients[i];
        final percentage = (client.value / totalRevenue * 100);

        if (percentage > 0.5) {
          // Seulement si le pourcentage est significatif
          chartData.add(
            PieChartSectionData(
              color: colors[i],
              value: client.value,
              title: '${percentage.toStringAsFixed(1)}%',
              radius: 55,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          );
        }
      }

      // Ajouter "Autres" si nécessaire
      if (othersRevenue > 0) {
        final othersPercentage = (othersRevenue / totalRevenue * 100);
        if (othersPercentage > 0.5) {
          chartData.add(
            PieChartSectionData(
              color: Colors.grey.shade400,
              value: othersRevenue,
              title: '${othersPercentage.toStringAsFixed(1)}%',
              radius: 55,
              titleStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          );
        }
      }

      // Informations sur le meilleur client
      String? bestClientName;
      double bestClientRevenue = 0.0;
      if (topClients.isNotEmpty) {
        bestClientName = clientNames[topClients.first.key];
        bestClientRevenue = topClients.first.value;
      }

      return {
        'chartData': chartData,
        'totalRevenue': totalRevenue,
        'bestClient': {'name': bestClientName, 'revenue': bestClientRevenue},
        'topClients':
            topClients
                .map(
                  (e) => {
                    'name': clientNames[e.key],
                    'revenue': e.value,
                    'percentage': (e.value / totalRevenue * 100),
                  },
                )
                .toList(),
      };
    } catch (e) {
      throw Exception('Erreur lors du chargement des données: $e');
    }
  }

  Widget _buildChartWidget(BuildContext context, Map<String, dynamic> data) {
    final chartData = data['chartData'] as List<PieChartSectionData>;
    final totalRevenue = data['totalRevenue'] as double;
    final bestClient = data['bestClient'] as Map<String, dynamic>?;
    final topClients = data['topClients'] as List<Map<String, dynamic>>;

    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Column(
      children: [
        // Titre et informations générales
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Répartition du CA',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1F2937),
                      fontSize: isSmallScreen ? 14 : 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(totalRevenue / 1000).toStringAsFixed(1)}k DT total',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                      fontSize: isSmallScreen ? 11 : 12,
                    ),
                  ),
                ],
              ),
            ),
            if (bestClient != null && bestClient['name'] != null)
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '🏆 Meilleur client',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: const Color(0xFF059669),
                        fontWeight: FontWeight.w600,
                        fontSize: isSmallScreen ? 9 : 10,
                      ),
                    ),
                    Text(
                      bestClient['name'],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1F2937),
                        fontSize: isSmallScreen ? 11 : 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${(bestClient['revenue'] / 1000).toStringAsFixed(1)}k DT',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: const Color(0xFF059669),
                        fontWeight: FontWeight.w600,
                        fontSize: isSmallScreen ? 9 : 10,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // Graphique circulaire
        SizedBox(
          height: isSmallScreen ? 120 : 140,
          child:
              isSmallScreen
                  ?
                  // Sur petit écran : graphique centré avec légende en dessous
                  Column(
                    children: [
                      Expanded(
                        child: PieChart(
                          PieChartData(
                            sections: chartData,
                            centerSpaceRadius: isSmallScreen ? 15 : 20,
                            sectionsSpace: 2,
                            startDegreeOffset: -90,
                          ),
                        ),
                      ),
                    ],
                  )
                  :
                  // Sur grand écran : graphique avec légende à côté
                  Row(
                    children: [
                      // Graphique
                      Expanded(
                        flex: 3,
                        child: PieChart(
                          PieChartData(
                            sections: chartData,
                            centerSpaceRadius: 20,
                            sectionsSpace: 2,
                            startDegreeOffset: -90,
                          ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Légende
                      Expanded(
                        flex: 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: _buildLegend(
                            topClients,
                            totalRevenue,
                            isSmallScreen,
                          ),
                        ),
                      ),
                    ],
                  ),
        ),

        // Légende en dessous pour les petits écrans
        if (isSmallScreen) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _buildLegend(topClients, totalRevenue, isSmallScreen),
          ),
        ],
      ],
    );
  }

  List<Widget> _buildLegend(
    List<Map<String, dynamic>> topClients,
    double totalRevenue, [
    bool isSmallScreen = false,
  ]) {
    final colors = [
      const Color(0xFF6366F1),
      const Color(0xFF8B5CF6),
      const Color(0xFF10B981),
      const Color(0xFFF59E0B),
      const Color(0xFFEF4444),
    ];

    List<Widget> legendItems = [];

    for (int i = 0; i < topClients.length && i < 4; i++) {
      final client = topClients[i];
      final percentage = client['percentage'] as double;

      if (percentage > 0.5) {
        legendItems.add(
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: isSmallScreen ? 1 : 2,
              horizontal: isSmallScreen ? 4 : 0,
            ),
            child: Row(
              mainAxisSize: isSmallScreen ? MainAxisSize.min : MainAxisSize.max,
              children: [
                Container(
                  width: isSmallScreen ? 6 : 8,
                  height: isSmallScreen ? 6 : 8,
                  decoration: BoxDecoration(
                    color: colors[i],
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 4 : 6),
                Flexible(
                  child: Text(
                    client['name'] ?? 'Client inconnu',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 8 : 10,
                      color: const Color(0xFF6B7280),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    // Ajouter "Autres" si nécessaire
    final othersPercentage = topClients
        .skip(4)
        .fold(0.0, (sum, client) => sum + (client['percentage'] as double));
    if (othersPercentage > 0.5) {
      legendItems.add(
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: isSmallScreen ? 1 : 2,
            horizontal: isSmallScreen ? 4 : 0,
          ),
          child: Row(
            mainAxisSize: isSmallScreen ? MainAxisSize.min : MainAxisSize.max,
            children: [
              Container(
                width: isSmallScreen ? 6 : 8,
                height: isSmallScreen ? 6 : 8,
                decoration: BoxDecoration(
                  color: Colors.grey.shade400,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: isSmallScreen ? 4 : 6),
              Flexible(
                child: Text(
                  'Autres',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 8 : 10,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return legendItems;
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 24),
            SizedBox(height: 8),
            Text(
              'Erreur de chargement',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics_outlined, color: Colors.grey, size: 24),
            SizedBox(height: 8),
            Text(
              'Aucune donnée disponible',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
