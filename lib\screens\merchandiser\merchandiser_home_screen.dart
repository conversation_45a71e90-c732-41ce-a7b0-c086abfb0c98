import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/rapport_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/mission.dart';
import '../merchandising/calendrier_missions_screen.dart';

class MerchandiserHomeScreen extends StatefulWidget {
  @override
  _MerchandiserHomeScreenState createState() => _MerchandiserHomeScreenState();
}

class _MerchandiserHomeScreenState extends State<MerchandiserHomeScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Différer le chargement des données après la construction du widget
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
    });
  }

  void _chargerDonnees() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    final rapportProvider = Provider.of<RapportProvider>(
      context,
      listen: false,
    );

    // Simuler un ID de merchandiser - vous devrez adapter selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';

    // Charger les missions du jour
    missionProvider.chargerMissionsDuJour(merchandiserId);
    missionProvider.chargerMissionsEnRetard(merchandiserId);

    // Charger les rapports
    rapportProvider.chargerRapportsParMerchandiser(merchandiserId);
    rapportProvider.chargerRapportsBrouillon(merchandiserId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('VitaBrosse - Merchandiser'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: [_buildDashboard(), CalendrierMissionsScreen()],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: [
          BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Vue'),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_month),
            label: 'Calendrier',
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard() {
    return Consumer3<MissionProvider, RapportProvider, AuthProvider>(
      builder: (
        context,
        missionProvider,
        rapportProvider,
        authProvider,
        child,
      ) {
        final missionsDuJour = missionProvider.missionsDuJour;
        final missionsEnRetard = missionProvider.missionsEnRetard;
        final rapportsBrouillon = rapportProvider.rapportsBrouillon;

        return RefreshIndicator(
          onRefresh: () async {
            _chargerDonnees();
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Salutation
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue, Colors.blue.shade300],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bonjour Merchandiser!',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Prêt pour une nouvelle journée de merchandising?',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24),

                // Statistiques rapides
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Missions du jour',
                        missionsDuJour.length.toString(),
                        Icons.today,
                        Colors.green,
                        () => setState(() => _currentIndex = 1),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: _buildStatCard(
                        'En retard',
                        missionsEnRetard.length.toString(),
                        Icons.schedule,
                        Colors.red,
                        () => setState(() => _currentIndex = 1),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Rapports brouillon',
                        rapportsBrouillon.length.toString(),
                        Icons.edit_note,
                        Colors.orange,
                        () => setState(() => _currentIndex = 3),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: _buildStatCard(
                        'Rapports envoyés',
                        rapportProvider.rapports
                            .where((r) => r.statut == 'envoye')
                            .length
                            .toString(),
                        Icons.send,
                        Colors.blue,
                        () => setState(() => _currentIndex = 3),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),

                // Missions urgentes
                if (missionsEnRetard.isNotEmpty) ...[
                  Text(
                    'Missions en retard',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 12),
                  ...missionsEnRetard
                      .take(3)
                      .map((mission) => _buildMissionCard(mission, true)),
                  SizedBox(height: 24),
                ],

                // Missions du jour
                if (missionsDuJour.isNotEmpty) ...[
                  Text(
                    'Missions du jour',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 12),
                  ...missionsDuJour
                      .take(3)
                      .map((mission) => _buildMissionCard(mission, false)),
                  SizedBox(height: 12),
                  if (missionsDuJour.length > 3)
                    TextButton(
                      onPressed: () => setState(() => _currentIndex = 1),
                      child: Text('Voir toutes les missions du jour'),
                    ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionCard(Mission mission, bool isUrgent) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isUrgent ? Border.all(color: Colors.red, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isUrgent ? Colors.red : Colors.blue,
          child: Icon(
            mission.estTerminee ? Icons.check : Icons.assignment,
            color: Colors.white,
          ),
        ),
        title: Text(
          mission.titre,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            decoration: mission.estTerminee ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(mission.clientNom),
            SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey),
                SizedBox(width: 4),
                Text(
                  '${mission.dateEcheance.day}/${mission.dateEcheance.month}/${mission.dateEcheance.year}',
                  style: TextStyle(
                    color: isUrgent ? Colors.red : Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Chip(
          label: Text(mission.statutAffichage, style: TextStyle(fontSize: 12)),
          backgroundColor: _getStatutColor(mission.statut),
        ),
        onTap: () {
          // Naviguer vers les détails de la mission
          setState(() => _currentIndex = 1);
        },
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange.shade100;
      case 'en_cours':
        return Colors.blue.shade100;
      case 'terminee':
        return Colors.green.shade100;
      case 'annulee':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }
}
